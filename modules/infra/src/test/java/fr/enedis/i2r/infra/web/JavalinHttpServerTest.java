package fr.enedis.i2r.infra.web;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.ModuleSecuritePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import fr.enedis.i2r.system.LoggingLoader;
import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;
import io.javalin.community.ssl.SslPlugin;

class JavalinHttpServerTest {

    @Mock
    private BipStatusManager bipStatusManager;
    @Mock
    private ComSiConfiguration comSiConfiguration;
    @Mock
    private ShellExecutorPort shellExecutorPort;
    @Mock
    private LoggingLoader loggingLoader;
    @Mock
    private SslPlugin sslPlugin;
    @Mock
    private ComsiParametersPort parametersPort;
    @Mock
    private BoardManagerPort boardManagerPort;
    @Mock
    private ModuleSecuritePort moduleSecuritePort;
    @Mock
    private ModemManagerPort modemManagerPort;
    @Mock
    private SiClientPort siClientPort;
    @Mock
    private ThreadWatchdog threadWatchdog;

    private JavalinHttpServer server;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        server = new JavalinHttpServer(
            bipStatusManager, comSiConfiguration, shellExecutorPort, loggingLoader, sslPlugin,
            parametersPort, boardManagerPort, moduleSecuritePort, modemManagerPort, siClientPort, threadWatchdog
        );
    }

    @Test
    void le_serveur_demarre_et_s_arrete_correctement() {
        assertTrue(server != null);
        assertFalse(server.isRunning());

        server.start();
        assertTrue(server.isRunning());

        server.stop();
        assertFalse(server.isRunning());
    }
}
