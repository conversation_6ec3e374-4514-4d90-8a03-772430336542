package fr.enedis.i2r.comsi;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.io.IOException;
import java.time.Clock;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.errors.RequestToSiException;
import fr.enedis.i2r.comsi.metrics.MetricsSourcePort;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.ConfigurationUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.ModuleSecuritePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;

public class ComSITest {

    private ExecutorService executor;
    private ComSI comSi;

    @BeforeEach
    void setup() throws Exception{
        executor = Executors.newSingleThreadExecutor();
    }

    @AfterEach
    void teardown() throws IOException {
        executor.shutdownNow();
    }

    @Test
    void la_configuration_s_envoie_quand_le_boitier_est_a_l_etat_init_au_demarrage() throws RequestToSiException, InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        var siClientPort = mock(SiClientPort.class);
        doAnswer(invocationOnMock -> {
            latch.countDown();
            return null;
        }).when(siClientPort).sendConfigurationBoitier(any());
        var customConf = new CustomComSiConfiguration();
        customConf.bipStatus = BipStatus.INIT;
        comSi = buildComSI(customConf.build(), siClientPort);

        executor.submit(comSi);

        boolean finished = latch.await(100, TimeUnit.MILLISECONDS);
        assertTrue(finished, "L'envoi de la config n'a pas été réalisé");
    }

    @Test
    void la_configuration_ne_s_envoie_pas_quand_le_boitier_est_a_l_etat_stable_au_demarrage() throws RequestToSiException, InterruptedException {
        var siClientPort = mock(SiClientPort.class);
        var customConf = new CustomComSiConfiguration();
        customConf.bipStatus = BipStatus.STABLE;
        comSi = buildComSI(customConf.build(), siClientPort);

        executor.submit(comSi);
        // Attendre 20ms pour que le thread ait le temps d'executer le code qu'on veut tester
        Thread.sleep(20);

        verify(siClientPort, never()).sendConfigurationBoitier(any());
    }

    private static ComSI buildComSI(ComSiConfiguration conf, SiClientPort siClientPort) {
        return new ComSI(conf,
            mock(ComsiParametersPort.class),
            siClientPort,
            mock(BoardManagerPort.class),
            mock(ModuleSecuritePort.class),
            mock(ModemManagerPort.class),
            mock(ConfigurationUpdateWatcherPort.class),
            mock(Clock.class),
            mock(ThreadWatchdog.class),
            mock(MetricsSourcePort.class));
    }

}
